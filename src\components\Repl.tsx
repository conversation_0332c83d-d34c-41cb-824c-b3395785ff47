"use client";

import { useCallback, useEffect } from "react";
import Menu from "./repl/Menu";
import Sidebar from "./repl/Sidebar";
import Editor from "./repl/Editor";
import FileTabs from "./repl/FileTabs";
import Output from "./repl/Output";

import Split from "react-split"; // react-split-pane alternative
import { useExecutionStore } from "@/components/repl/state";
import { tryPlausible } from "@/lib/utilities";

export default function Repl({
  enableMenu = true,
  enableSidebar = true,
}: {
  enableMenu?: boolean;
  enableSidebar?: boolean;
}) {
  const { setRunCode, isSaved } = useExecutionStore();

  const handleBeforeUnload = useCallback(
    (evt: BeforeUnloadEvent) => {
      if (!isSaved && window.parent === window) {
        evt.preventDefault();
        evt.returnValue = "";
      }
    },
    [isSaved]
  );

  const handleRun = async () => {
    tryPlausible("Compile");
    setRunCode(true);
  };

  const handleShare = async () => {
    tryPlausible("Share");
    await navigator.clipboard.writeText(window.location.toString());
  };

  useEffect(() => {
    if (!isSaved) {
      window.addEventListener("beforeunload", handleBeforeUnload);
      return () =>
        window.removeEventListener("beforeunload", handleBeforeUnload);
    }
  }, [isSaved, handleBeforeUnload]);

  return (
    <div className="w-full h-screen font-sans flex flex-col overflow-hidden">
      {enableMenu && <Menu onShare={handleShare} onRun={handleRun} />}

      <div className="flex items-stretch flex-1 overflow-hidden">
        {enableSidebar && <Sidebar />}

        <div className="flex-1 overflow-hidden">
          <Split
            className="split flex flex-col h-full"
            direction="vertical"
            minSize={64}
            gutterSize={6}
            style={{ display: "flex", height: "100%" }}
          >
            <section className="h-1/2 flex flex-col">
              <div className="text-sm">
                <FileTabs />
              </div>
              <Editor />
            </section>

            <section className="border-t border-stone-200 dark:border-stone-700 overflow-hidden h-1/2">
              <Output />
            </section>
          </Split>
        </div>
      </div>
    </div>
  );
}
