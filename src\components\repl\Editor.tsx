"use client";

import { useEffect, useRef, useCallback } from "react";
import { basicSetup } from "codemirror";
import { EditorView, keymap } from "@codemirror/view";
import { Compartment, EditorState } from "@codemirror/state";
import { indentWithTab } from "@codemirror/commands";
import { indentUnit } from "@codemirror/language";
import { lintGutter } from "@codemirror/lint";
import { java } from "@codemirror/lang-java";
import {
  useFilesStore,
  useFiddleMetaStore,
  useExecutionStore,
  useSettingsStore,
} from "./state";
import { compress } from "@/lib/compress-fiddle";
import { coolGlow, tomorrow } from "thememirror";
import { compartment, diagnostic, parseCompileLog } from "./linter";
import "./codemirror.css";

export default function Editor() {
  const containerRef = useRef<HTMLDivElement>(null);
  const editorViewRef = useRef<EditorView | null>(null);
  const editorStatesRef = useRef<Map<string, EditorState>>(new Map());
  const themeCompartmentRef = useRef(new Compartment());
  const skipResetRef = useRef(false);

  const { files, selectedFilePath, updateFile } = useFilesStore();
  const { fiddleTitle, fiddleUpdated } = useFiddleMetaStore();
  const { compileLog } = useExecutionStore();
  const { theme } = useSettingsStore();

  const currentTheme = theme === "dark" ? coolGlow : tomorrow;

  const extensions = useCallback(
    () => [
      basicSetup,
      keymap.of([indentWithTab]),
      indentUnit.of("    "),
      lintGutter(),
      compartment.of(diagnostic.of([])),
      themeCompartmentRef.current.of(currentTheme),
    ],
    [currentTheme]
  );

  const updateFragmentURL = useCallback(() => {
    if (typeof window !== "undefined") {
      const fiddle = {
        title: fiddleTitle,
        files,
        updated: fiddleUpdated,
      };
      const compressed = compress(fiddle);
      window.history.replaceState(null, "", `#${compressed}`);
    }
  }, [files, fiddleTitle, fiddleUpdated]);

  // Reset editor states when files change
  const resetEditorStates = useCallback(
    (newFiles: typeof files) => {
      if (skipResetRef.current) return;

      const editorStates = editorStatesRef.current;

      for (const file of newFiles) {
        let state = editorStates.get(file.path);
        if (state) {
          // Update state to match filesystem
          const existing = state.doc.toString();
          if (file.content !== existing) {
            const transaction = state.update({
              changes: {
                from: 0,
                to: existing.length,
                insert: file.content,
              },
            });
            state = transaction.state;
          }
        } else {
          const extension = file.path.split(".").pop();
          state = EditorState.create({
            doc: file.content,
            extensions:
              extension === "java" ? [...extensions(), java()] : extensions(),
          });
        }

        editorStates.set(file.path, state);
        if (file.path === selectedFilePath && editorViewRef.current) {
          editorViewRef.current.setState(state);
        }
      }
    },
    [extensions, selectedFilePath]
  );

  // Initialize editor
  useEffect(() => {
    if (!containerRef.current) return;

    const editorView = new EditorView({
      parent: containerRef.current,
      state: editorStatesRef.current.get(selectedFilePath),
      dispatch: (transaction) => {
        editorView.update([transaction]);
        editorStatesRef.current.set(selectedFilePath, transaction.state);

        if (transaction.docChanged) {
          skipResetRef.current = true;
          updateFile(selectedFilePath, transaction.state.doc.toString());
          setTimeout(() => {
            skipResetRef.current = false;
            updateFragmentURL();
          }, 0);
        }
      },
    });

    editorViewRef.current = editorView;

    return () => {
      editorView.destroy();
      editorViewRef.current = null;
    };
  }, [selectedFilePath, updateFile, updateFragmentURL]);

  // Update editor when selected file changes
  useEffect(() => {
    const state = editorStatesRef.current.get(selectedFilePath);
    if (state && editorViewRef.current) {
      editorViewRef.current.setState(state);
    }
  }, [selectedFilePath]);

  // Reset states when files change
  useEffect(() => {
    resetEditorStates(files);
  }, [files, resetEditorStates]);

  // Update theme
  useEffect(() => {
    if (editorViewRef.current) {
      editorViewRef.current.dispatch({
        effects: themeCompartmentRef.current.reconfigure(currentTheme),
      });
    }
  }, [currentTheme]);

  // Update linting when compile log changes
  useEffect(() => {
    if (editorViewRef.current && compileLog) {
      const diagnostics = parseCompileLog(compileLog, files);
      const currentFileIndex = files.findIndex(
        (f) => f.path === selectedFilePath
      );
      const currentFileDiagnostics = diagnostics[currentFileIndex] || [];
      editorViewRef.current.dispatch({
        effects: compartment.reconfigure(diagnostic.of(currentFileDiagnostics)),
      });
    }
  }, [compileLog, files, selectedFilePath]);

  return (
    <div className="flex-1 overflow-hidden">
      <div ref={containerRef} className="h-full" />
    </div>
  );
}
