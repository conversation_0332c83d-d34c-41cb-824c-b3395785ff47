// app/output/page.tsx
"use client";

import Output from "@/components/repl/Output";
import { useSettingsStore } from "@/components/repl/state";
import { useEffect } from "react";

export default function OutputPage() {
  const { theme } = useSettingsStore();

  // Apply theme to document element
  useEffect(() => {
    if (typeof window === "undefined") return;

    const applyTheme = () => {
      const root = document.documentElement;
      if (theme === "dark") {
        root.classList.add("dark");
      } else if (theme === "light") {
        root.classList.remove("dark");
      } else {
        // System theme
        const prefersDark = window.matchMedia(
          "(prefers-color-scheme: dark)"
        ).matches;
        if (prefersDark) {
          root.classList.add("dark");
        } else {
          root.classList.remove("dark");
        }
      }
    };

    applyTheme();

    if (theme === "system") {
      const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
      mediaQuery.addEventListener("change", applyTheme);
      return () => mediaQuery.removeEventListener("change", applyTheme);
    }
  }, [theme]);

  return (
    <div className="w-screen h-screen bg-white text-black dark:bg-stone-900 dark:text-white">
      <Output isOutputMode={false} />
    </div>
  );
}
