// app/layout.tsx
import "./globals.css";
import { ReactNode } from "react";
import { Metadata } from "next";
import Script from "next/script";
import ClientLayout from "./ClientLayout";

export const metadata: Metadata = {
  title: "JavaFiddle - Build and share Java code snippets in your browser",
  description:
    "JavaFiddle is an online, browser-based Java IDE. Create and share Swing applications for free!",
};

export default function RootLayout({ children }: { children: ReactNode }) {
  return (
    <html lang="en">
      <head>
        <meta name="darkreader-lock" />
      </head>
      <body className="contents overflow-hidden">
        <Script
          defer
          data-domain="javafiddle.leaningtech.com"
          src="https://plausible.leaningtech.com/js/script.js"
          strategy="afterInteractive"
        />
        <Script
          src="https://cjrtnc.leaningtech.com/4.2/loader.js"
          strategy="beforeInteractive"
        />
        <ClientLayout>{children}</ClientLayout>
      </body>
    </html>
  );
}
