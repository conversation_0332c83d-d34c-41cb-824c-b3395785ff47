"use client";

import { useSettingsStore } from "../state";

export default function ThemeSwitcher() {
  const { theme, setTheme } = useSettingsStore();

  return (
    <select
      value={theme}
      onChange={(e) => setTheme(e.target.value as 'light' | 'dark' | 'system')}
      className="text-sm bg-stone-100 dark:bg-stone-700 border border-stone-300 dark:border-stone-600 rounded px-2 py-1 text-stone-900 dark:text-stone-100"
    >
      <option value="system">System</option>
      <option value="light">Light</option>
      <option value="dark">Dark</option>
    </select>
  );
}
