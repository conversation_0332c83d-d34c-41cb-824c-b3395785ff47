"use client";

import { ExternalLink } from "lucide-react";
import { useExecutionStore } from "./state";
import CheerpJ from "../CheerpJ";
import Loading from "../Loading";

interface OutputProps {
  isOutputMode?: boolean;
}

export default function Output({ isOutputMode = true }: OutputProps) {
  const { isRunning } = useExecutionStore();

  const viewInOutputMode = () => {
    if (typeof window !== "undefined") {
      const url = `/output${window.location.hash}`;
      window.open(url, "_blank", "noreferrer");
    }
  };

  return (
    <>
      {/* Loading overlay */}
      {isRunning && (
        <div className="w-full h-full">
          <Loading />
        </div>
      )}

      {/* Main output container */}
      <div className={`w-full h-full flex ${isRunning ? "hidden" : ""}`}>
        <section className="flex flex-col w-1/2 border-r border-stone-200 dark:border-stone-700">
          <div className="p-3 text-stone-500 text-sm select-none">Console</div>
          <div className="grow overflow-auto">
            {/* CheerpJ implicitly looks for a #console to write to */}
            <pre
              className="font-mono text-sm p-2 whitespace-pre-wrap"
              id="console"
            />
          </div>
        </section>

        <section className="flex flex-col w-1/2">
          <div className="p-3 text-stone-500 text-sm select-none">Result</div>
          <div className="grow relative overflow-auto" id="output">
            {/* CheerpJ display will be inserted here */}
          </div>
        </section>
      </div>

      {/* Output mode button */}
      {isOutputMode && (
        <div className="absolute top-1/2 right-0 text-stone-500 text-sm flex items-center select-none">
          <button
            className="px-2 py-2 hover:text-stone-700 dark:hover:text-stone-300"
            title="Open in new tab"
            onClick={viewInOutputMode}
          >
            <ExternalLink className="w-5 h-5" />
          </button>
        </div>
      )}

      {/* CheerpJ component */}
      <CheerpJ />
    </>
  );
}
