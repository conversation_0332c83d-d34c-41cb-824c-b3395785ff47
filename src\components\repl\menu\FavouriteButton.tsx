"use client";

import { Heart } from "lucide-react";
import { useFilesStore, useFiddleMetaStore, useFavouritesStore } from "../state";

export default function FavouriteButton() {
  const { files } = useFilesStore();
  const { fiddleTitle, fiddleUpdated, favouriteIndex, setFavouriteIndex } = useFiddleMetaStore();
  const { favourites, addFavourite, removeFavourite } = useFavouritesStore();

  const isFavourite = favouriteIndex !== -1;

  const handleToggleFavourite = () => {
    if (isFavourite) {
      // Remove from favourites
      removeFavourite(favouriteIndex);
      setFavouriteIndex(-1);
    } else {
      // Add to favourites
      const fiddle = {
        title: fiddleTitle,
        files,
        updated: fiddleUpdated
      };
      addFavourite(fiddle);
      setFavouriteIndex(favourites.length); // Will be the new index after adding
    }
  };

  return (
    <li>
      <button
        onClick={handleToggleFavourite}
        className={`text-sm flex items-center rounded font-semibold px-2 py-1 h-8 ${
          isFavourite
            ? 'bg-red-100 hover:bg-red-200 dark:bg-red-900 dark:hover:bg-red-800 text-red-600 dark:text-red-400'
            : 'bg-stone-100 hover:bg-stone-200 dark:bg-stone-800 dark:hover:bg-stone-700'
        }`}
        title={isFavourite ? "Remove from favourites" : "Add to favourites"}
      >
        <Heart 
          className={`w-4 h-4 ${isFavourite ? 'fill-current' : ''}`} 
        />
      </button>
    </li>
  );
}
