import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import type { Fiddle } from "@/lib/compress-fiddle";

export type File = {
  path: string;
  content: string;
};

// Files store
interface FilesState {
  files: File[];
  selectedFilePath: string;
  setFiles: (files: File[]) => void;
  updateFile: (path: string, content: string) => void;
  addFile: (file: File) => void;
  removeFile: (path: string) => void;
  setSelectedFilePath: (path: string) => void;
}

export const useFilesStore = create<FilesState>((set) => ({
  files: [],
  selectedFilePath: "Main.java",
  setFiles: (files) => set({ files }),
  updateFile: (path, content) =>
    set((state) => ({
      files: state.files.map((file) =>
        file.path === path ? { ...file, content } : file
      ),
    })),
  addFile: (file) =>
    set((state) => ({
      files: [...state.files, file],
    })),
  removeFile: (path) =>
    set((state) => ({
      files: state.files.filter((file) => file.path !== path),
      selectedFilePath:
        state.selectedFilePath === path
          ? state.files.find((f) => f.path !== path)?.path || "Main.java"
          : state.selectedFilePath,
    })),
  setSelectedFilePath: (path) => set({ selectedFilePath: path }),
}));

// Fiddle metadata store
interface FiddleMetaState {
  fiddleTitle: string;
  fiddleUpdated?: Date;
  favouriteIndex: number;
  setFiddleTitle: (title: string) => void;
  setFiddleUpdated: (date?: Date) => void;
  setFavouriteIndex: (index: number) => void;
}

export const useFiddleMetaStore = create<FiddleMetaState>((set) => ({
  fiddleTitle: "",
  fiddleUpdated: undefined,
  favouriteIndex: -1,
  setFiddleTitle: (title) => set({ fiddleTitle: title }),
  setFiddleUpdated: (date) => set({ fiddleUpdated: date }),
  setFavouriteIndex: (index) => set({ favouriteIndex: index }),
}));

// Execution state store
interface ExecutionState {
  isRunning: boolean;
  isSaved: boolean;
  runCode: boolean;
  compileLog: string;
  setIsRunning: (val: boolean) => void;
  setIsSaved: (val: boolean) => void;
  setRunCode: (val: boolean) => void;
  setCompileLog: (log: string) => void;
}

export const useExecutionStore = create<ExecutionState>((set) => ({
  isRunning: false,
  isSaved: true,
  runCode: false,
  compileLog: "",
  setIsRunning: (val) => set({ isRunning: val }),
  setIsSaved: (val) => set({ isSaved: val }),
  setRunCode: (val) => set({ runCode: val }),
  setCompileLog: (log) => set({ compileLog: log }),
}));

// Settings store (persisted)
interface SettingsState {
  isSidebarOpen: boolean;
  autoRun: boolean;
  theme: "light" | "dark" | "system";
  setIsSidebarOpen: (open: boolean) => void;
  setAutoRun: (val: boolean) => void;
  setTheme: (theme: "light" | "dark" | "system") => void;
}

export const useSettingsStore = create<SettingsState>()(
  persist(
    (set) => ({
      isSidebarOpen: true,
      autoRun: false,
      theme: "system",
      setIsSidebarOpen: (open) => set({ isSidebarOpen: open }),
      setAutoRun: (val) => set({ autoRun: val }),
      setTheme: (theme) => set({ theme }),
    }),
    {
      name: "javafiddle-settings",
      storage: createJSONStorage(() => localStorage),
    }
  )
);

// Favourites store (persisted)
interface FavouritesState {
  favourites: Fiddle[];
  setFavourites: (favs: Fiddle[]) => void;
  addFavourite: (fiddle: Fiddle) => void;
  removeFavourite: (index: number) => void;
  loadFavourite: (index: number) => void;
}

export const useFavouritesStore = create<FavouritesState>()(
  persist(
    (set, get) => ({
      favourites: [],
      setFavourites: (favs) => set({ favourites: favs }),
      addFavourite: (fiddle) =>
        set((state) => ({
          favourites: [...state.favourites, fiddle],
        })),
      removeFavourite: (index) =>
        set((state) => ({
          favourites: state.favourites.filter((_, i) => i !== index),
        })),
      loadFavourite: (index) => {
        const favourite = get().favourites[index];
        if (favourite) {
          useFilesStore.getState().setFiles(favourite.files);
          useFiddleMetaStore.getState().setFiddleTitle(favourite.title);
          useFiddleMetaStore.getState().setFiddleUpdated(favourite.updated);
          useFiddleMetaStore.getState().setFavouriteIndex(index);
        }
      },
    }),
    {
      name: "javafiddle-favourites",
      storage: createJSONStorage(() => localStorage),
    }
  )
);

// Convenience exports for backward compatibility
export const files = useFilesStore;
export const selectedFilePath = useFilesStore;
export const isSidebarOpen = useSettingsStore;
export const fiddleTitle = useFiddleMetaStore;
export const fiddleUpdated = useFiddleMetaStore;
export const favourites = useFavouritesStore;
export const favouriteIndex = useFiddleMetaStore;
export const autoRun = useSettingsStore;
export const isRunning = useExecutionStore;
export const isSaved = useExecutionStore;
export const runCode = useExecutionStore;
export const compileLog = useExecutionStore;
