import lz from "lz-string";
import { z } from "zod";

// Zod schema for type-safe validation
const fiddle = z.object({
  files: z.array(
    z.object({
      path: z.string(),
      content: z.string(),
    })
  ),
  title: z.string(),
  updated: z.coerce.date().optional(),
});

export type Fiddle = z.infer<typeof fiddle>;

// Compress fiddle data to a URL-safe string
export function compress(data: Fiddle): string {
  return lz.compressToEncodedURIComponent(JSON.stringify(data));
}

// Decompress and validate fiddle string
export function decompress(str: string): Fiddle {
  return fiddle.parse(JSON.parse(lz.decompressFromEncodedURIComponent(str)));
}

// Default Java fiddle (used on initial load)
export const defaultFiddle: Fiddle = {
  title: "",
  files: [
    {
      path: "Main.java",
      content: `package fiddle;

class Main {
  public static void main(String[] args) {
    System.out.println("Hello, World!");
  }
}
`,
    },
  ],
};

export const defaultFiddleComp = compress(defaultFiddle);
