"use client";

import { useFilesStore } from "./state";
import FileTab from "./FileTab";
import { Plus } from "lucide-react";

export default function FileTabs() {
  const { files, addFile } = useFilesStore();

  const getNewFilePath = (): string => {
    let i = 2;
    let path = `Class.java`;
    while (files.find((f) => f.path === path)) {
      path = `Class${i}.java`;
      i++;
    }
    return path;
  };

  const handleAddFile = () => {
    const path = getNewFilePath();
    const className = path.replace(/\.java$/, '');
    const newFile = {
      path,
      content: `package fiddle;

class ${className} {
    public ${className}() {
        // TODO
    }
}
`
    };
    addFile(newFile);
  };

  return (
    <div className="flex items-stretch text-stone-500 h-8">
      {files.map((file, i) => (
        <FileTab key={file.path} file={file} canEdit={i > 0} />
      ))}
      <button
        className="mx-1 px-2 hover:text-stone-800 dark:hover:text-stone-400"
        title="New file"
        onClick={handleAddFile}
      >
        <Plus className="w-4 h-4" />
      </button>
    </div>
  );
}
