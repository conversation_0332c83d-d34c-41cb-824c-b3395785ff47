"use client";

import { useState, useRef, useEffect } from "react";
import { X } from "lucide-react";
import { useFilesStore, type File } from "./state";

interface FileTabProps {
  file: File;
  canEdit: boolean;
}

export default function FileTab({ file, canEdit }: FileTabProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editPath, setEditPath] = useState("");
  const inputRef = useRef<HTMLInputElement>(null);

  const { files, selectedFilePath, setSelectedFilePath, removeFile } =
    useFilesStore();

  const isSelected = selectedFilePath === file.path;

  // Remove extension (.java) from path for display
  const displayPath = file.path.substring(0, file.path.lastIndexOf("."));

  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
      inputRef.current.select();
    }
  }, [isEditing]);

  const handleStartEdit = () => {
    if (isSelected && canEdit) {
      setIsEditing(true);
      setEditPath(displayPath);
    }
  };

  const handleFinishEdit = () => {
    setIsEditing(false);

    if (editPath + ".java" !== file.path) {
      const newPath = editPath + ".java";
      const existingFile = files.find((f) => f.path === newPath);

      if (existingFile) {
        alert(`A file with the name '${editPath}' already exists!`);
        return;
      }

      // Update the file path
      const updatedFiles = files.map((f) =>
        f.path === file.path ? { ...f, path: newPath } : f
      );

      // This is a bit hacky but we need to update the entire files array
      // since we're changing the path which is the key
      useFilesStore.setState({ files: updatedFiles });
      setSelectedFilePath(newPath);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleFinishEdit();
    } else if (e.key === "Escape") {
      setIsEditing(false);
      setEditPath(displayPath);
    }
  };

  const handleRemove = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (confirm(`Are you sure you want to delete ${file.path}?`)) {
      removeFile(file.path);
    }
  };

  return (
    <button
      className={`group px-3 py-2 hover:text-stone-800 dark:hover:text-stone-400 cursor-pointer flex items-center justify-center ${
        isSelected ? "!text-stone-600 dark:!text-stone-200" : ""
      }`}
      onClick={() => setSelectedFilePath(file.path)}
    >
      {isEditing ? (
        <input
          ref={inputRef}
          type="text"
          value={editPath}
          onChange={(e) => setEditPath(e.target.value)}
          onBlur={handleFinishEdit}
          onKeyDown={handleKeyDown}
          className="bg-transparent border-none outline-none focus:text-orange-600 min-w-0"
          style={{ width: `${Math.max(editPath.length, 4)}ch` }}
        />
      ) : (
        <span
          onClick={handleStartEdit}
          className={`${isSelected && canEdit ? "cursor-text" : ""} ${
            !isSelected || !canEdit ? "pointer-events-none" : ""
          }`}
        >
          {displayPath}
        </span>
      )}

      {canEdit && (
        <button
          onClick={handleRemove}
          className="ml-1 opacity-0 group-hover:opacity-100 hover:text-red-500 transition-opacity"
          title="Delete file"
        >
          <X className="w-3 h-3" />
        </button>
      )}
    </button>
  );
}
