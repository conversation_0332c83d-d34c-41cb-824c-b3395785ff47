{"name": "javafiddle", "version": "0.0.1", "license": "Apache-2.0", "private": true, "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "lint": "prettier --plugin-search-dir . --check .", "format": "prettier --plugin-search-dir . --write ."}, "devDependencies": {"@sveltejs/adapter-auto": "^2.0.0", "@sveltejs/kit": "^1.20.4", "autoprefixer": "^10.4.15", "postcss": "^8.4.29", "prettier": "^2.8.0", "prettier-plugin-svelte": "^2.10.1", "svelte": "^4.0.5", "svelte-check": "^3.4.3", "tailwindcss": "^3.3.3", "tslib": "^2.4.1", "typescript": "^5.0.0", "vite": "^4.4.2"}, "type": "module", "dependencies": {"@codemirror/commands": "^6.2.5", "@codemirror/lang-java": "^6.0.1", "@codemirror/language": "^6.9.0", "@codemirror/lint": "^6.4.1", "@codemirror/state": "^6.2.1", "@codemirror/view": "^6.17.1", "@iconify/svelte": "^3.1.4", "@macfja/svelte-persistent-store": "^2.4.0", "@rich_harris/svelte-split-pane": "^1.1.1", "codemirror": "^6.0.1", "lz-string": "^1.5.0", "svelte-relative-time": "^0.0.4", "thememirror": "^2.0.1", "zod": "^3.22.2"}}