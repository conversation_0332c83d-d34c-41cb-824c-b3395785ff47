"use client";

import { useState, useEffect, useCallback } from "react";
import { Play } from "lucide-react";
import { useFilesStore, useFiddleMetaStore, useSettingsStore } from "./state";
import { defaultFiddle, defaultFiddleComp } from "@/lib/compress-fiddle";
import FiddleTitle from "./menu/FiddleTitle";
import SettingsButton from "./menu/SettingsButton";
import FavouriteButton from "./menu/FavouriteButton";
import { formatDistanceToNow } from "date-fns";

interface MenuProps {
  onShare: () => void;
  onRun: () => void;
}

export default function Menu({ onShare, onRun }: MenuProps) {
  const [showShareMessage, setShowShareMessage] = useState(false);

  const { setFiles } = useFilesStore();
  const { fiddleUpdated, setFiddleTitle, setFiddleUpdated, setFavouriteIndex } =
    useFiddleMetaStore();
  const { autoRun } = useSettingsStore();

  // Handle share message timeout
  useEffect(() => {
    if (showShareMessage) {
      const timeoutId = setTimeout(() => {
        setShowShareMessage(false);
      }, 800);
      return () => clearTimeout(timeoutId);
    }
  }, [showShareMessage]);

  // Handle keyboard shortcuts
  const handleKeyDown = useCallback(
    (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key === "s") {
        e.preventDefault();
        onShare();
      }
    },
    [onShare]
  );

  useEffect(() => {
    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [handleKeyDown]);

  const createNewFile = () => {
    setFiles(defaultFiddle.files);
    setFiddleTitle(defaultFiddle.title);
    setFiddleUpdated(defaultFiddle.updated);
    setFavouriteIndex(-1);

    if (typeof window !== "undefined") {
      window.history.pushState(null, "", `/#${defaultFiddleComp}`);
    }
  };

  const handleShare = () => {
    onShare();
    setShowShareMessage(true);
  };

  return (
    <header className="px-4 h-16 flex items-center justify-between gap-4 relative shadow dark:shadow-none dark:border-b border-b-stone-700 dark:bg-stone-800">
      <button
        className="text-xl text-orange-500 dark:text-orange-400 font-bold"
        onClick={createNewFile}
      >
        <h1>JavaFiddle</h1>
      </button>

      <div className="grow flex flex-col justify-center self-stretch">
        <FiddleTitle />
        {fiddleUpdated && (
          <div className="h-4 leading-3 text-xs text-stone-500 dark:text-stone-400">
            {formatDistanceToNow(fiddleUpdated, { addSuffix: true })}
          </div>
        )}
      </div>

      <ul className="flex items-center gap-2">
        {showShareMessage && (
          <li className="text-xs text-stone-600 dark:text-stone-400 animate-in fade-in duration-100">
            URL copied to clipboard
          </li>
        )}

        <FavouriteButton />

        {!autoRun && (
          <li>
            <button
              onClick={onRun}
              className="text-sm flex items-center rounded bg-stone-100 hover:bg-stone-200 dark:bg-stone-800 dark:hover:bg-stone-700 font-semibold px-2 py-1 h-8"
            >
              <Play className="w-4 h-4 mr-1" />
              Run
            </button>
          </li>
        )}

        <li>
          <button
            onClick={handleShare}
            className="text-sm flex items-center rounded bg-stone-100 hover:bg-stone-200 dark:bg-stone-800 dark:hover:bg-stone-700 font-semibold px-2 py-1 h-8"
          >
            Share
          </button>
        </li>

        <SettingsButton />
      </ul>
    </header>
  );
}
