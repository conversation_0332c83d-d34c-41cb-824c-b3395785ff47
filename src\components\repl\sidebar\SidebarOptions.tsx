"use client";

import { useState } from "react";
import { Icon } from "@iconify/react";
import { useFavouritesStore } from "../state";
import { formatDistanceToNow } from "date-fns";

interface SidebarOptionsProps {
  forceClose: boolean;
  onSelectOption: () => void;
}

export default function SidebarOptions({ forceClose, onSelectOption }: SidebarOptionsProps) {
  const [activeTab, setActiveTab] = useState<'favourites' | 'examples'>('favourites');
  const { favourites, loadFavourite } = useFavouritesStore();

  const handleSelectFavourite = (index: number) => {
    loadFavourite(index);
    onSelectOption();
  };

  const examples = [
    {
      title: "Hello World",
      description: "A simple Hello World program",
      files: [
        {
          path: "Main.java",
          content: `package fiddle;

class Main {
    public static void main(String[] args) {
        System.out.println("Hello, World!");
    }
}
`
        }
      ]
    },
    {
      title: "Fibonacci Sequence",
      description: "Calculate Fibonacci numbers",
      files: [
        {
          path: "Main.java",
          content: `package fiddle;

class Main {
    public static void main(String[] args) {
        int n = 10;
        System.out.println("First " + n + " Fibonacci numbers:");
        
        for (int i = 0; i < n; i++) {
            System.out.print(fibonacci(i) + " ");
        }
        System.out.println();
    }
    
    static int fibonacci(int n) {
        if (n <= 1) return n;
        return fibonacci(n - 1) + fibonacci(n - 2);
    }
}
`
        }
      ]
    }
  ];

  if (forceClose) {
    return null;
  }

  return (
    <div className="flex flex-col h-full">
      {/* Tab buttons */}
      <div className="flex border-b border-stone-300 dark:border-stone-600">
        <button
          onClick={() => setActiveTab('favourites')}
          className={`flex-1 px-4 py-3 text-sm font-medium ${
            activeTab === 'favourites'
              ? 'text-orange-600 dark:text-orange-400 border-b-2 border-orange-600 dark:border-orange-400'
              : 'text-stone-600 dark:text-stone-400 hover:text-stone-800 dark:hover:text-stone-200'
          }`}
        >
          <Icon icon="mdi:heart" className="w-4 h-4 inline mr-2" />
          Favourites
        </button>
        <button
          onClick={() => setActiveTab('examples')}
          className={`flex-1 px-4 py-3 text-sm font-medium ${
            activeTab === 'examples'
              ? 'text-orange-600 dark:text-orange-400 border-b-2 border-orange-600 dark:border-orange-400'
              : 'text-stone-600 dark:text-stone-400 hover:text-stone-800 dark:hover:text-stone-200'
          }`}
        >
          <Icon icon="mdi:code-braces" className="w-4 h-4 inline mr-2" />
          Examples
        </button>
      </div>

      {/* Tab content */}
      <div className="flex-1 overflow-y-auto">
        {activeTab === 'favourites' ? (
          <div className="p-4">
            {favourites.length === 0 ? (
              <div className="text-center text-stone-500 dark:text-stone-400 py-8">
                <Icon icon="mdi:heart-outline" className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p>No favourites yet</p>
                <p className="text-sm mt-2">Click the heart icon to save your fiddles</p>
              </div>
            ) : (
              <div className="space-y-3">
                {favourites.map((favourite, index) => (
                  <button
                    key={index}
                    onClick={() => handleSelectFavourite(index)}
                    className="w-full text-left p-3 rounded-lg bg-stone-50 dark:bg-stone-700 hover:bg-stone-100 dark:hover:bg-stone-600 transition-colors"
                  >
                    <div className="font-medium text-stone-900 dark:text-stone-100 mb-1">
                      {favourite.title || "Untitled"}
                    </div>
                    {favourite.updated && (
                      <div className="text-xs text-stone-500 dark:text-stone-400">
                        {formatDistanceToNow(favourite.updated, { addSuffix: true })}
                      </div>
                    )}
                  </button>
                ))}
              </div>
            )}
          </div>
        ) : (
          <div className="p-4">
            <div className="space-y-3">
              {examples.map((example, index) => (
                <button
                  key={index}
                  onClick={() => {
                    // Load example
                    // This would need to be implemented in the store
                    onSelectOption();
                  }}
                  className="w-full text-left p-3 rounded-lg bg-stone-50 dark:bg-stone-700 hover:bg-stone-100 dark:hover:bg-stone-600 transition-colors"
                >
                  <div className="font-medium text-stone-900 dark:text-stone-100 mb-1">
                    {example.title}
                  </div>
                  <div className="text-xs text-stone-500 dark:text-stone-400">
                    {example.description}
                  </div>
                </button>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
