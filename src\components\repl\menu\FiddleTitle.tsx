"use client";

import { useState, useRef, useEffect } from "react";
import { useFiddleMetaStore } from "../state";

export default function FiddleTitle() {
  const [isEditing, setIsEditing] = useState(false);
  const [editTitle, setEditTitle] = useState("");
  const inputRef = useRef<HTMLInputElement>(null);
  
  const { fiddleTitle, setFiddleTitle } = useFiddleMetaStore();

  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
      inputRef.current.select();
    }
  }, [isEditing]);

  const handleStartEdit = () => {
    setIsEditing(true);
    setEditTitle(fiddleTitle);
  };

  const handleFinishEdit = () => {
    setIsEditing(false);
    setFiddleTitle(editTitle.trim());
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleFinishEdit();
    } else if (e.key === 'Escape') {
      setIsEditing(false);
      setEditTitle(fiddleTitle);
    }
  };

  const displayTitle = fiddleTitle || "Untitled";

  return (
    <div className="h-6 leading-6">
      {isEditing ? (
        <input
          ref={inputRef}
          type="text"
          value={editTitle}
          onChange={(e) => setEditTitle(e.target.value)}
          onBlur={handleFinishEdit}
          onKeyDown={handleKeyDown}
          className="bg-transparent border-none outline-none text-stone-900 dark:text-stone-100 font-medium w-full"
          placeholder="Untitled"
        />
      ) : (
        <button
          onClick={handleStartEdit}
          className="text-stone-900 dark:text-stone-100 font-medium hover:text-stone-700 dark:hover:text-stone-300 text-left w-full"
        >
          {displayTitle}
        </button>
      )}
    </div>
  );
}
