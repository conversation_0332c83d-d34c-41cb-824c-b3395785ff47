// lib/utilities.ts

declare global {
  interface Window {
    plausible?: (eventName: string) => void;
  }
}

// Track analytics events safely (works even if Plausible is blocked)
export function tryPlausible(eventName: string) {
  if (typeof window !== "undefined" && typeof window.plausible === "function") {
    window.plausible(eventName);
  }
}

// Debounce wrapper
export function debounceFunction(fn: () => void, delay: number) {
  let timeoutId: ReturnType<typeof setTimeout>;
  return () => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(fn, delay);
  };
}
