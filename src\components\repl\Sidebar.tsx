"use client";

import { Icon } from "@iconify/react";
import { motion, AnimatePresence } from "framer-motion";
import SidebarOptions from "./sidebar/SidebarOptions";
import { useSettingsStore } from "./state";

export default function Sidebar() {
  const { isSidebarOpen, setIsSidebarOpen } = useSettingsStore();

  return (
    <aside
      className={`transition-[width] flex flex-col overflow-hidden flex-shrink-0 bg-stone-100 dark:bg-stone-800 ${
        isSidebarOpen ? "w-80" : "w-10"
      }`}
    >
      <div
        className={`text-right shadow-none animate-shadow ${
          isSidebarOpen ? "!shadow" : ""
        }`}
      >
        <button
          className="w-10 h-10 text-stone-600 hover:text-stone-900 dark:text-stone-400 dark:hover:text-stone-100 inline-flex items-center justify-center"
          onClick={() => setIsSidebarOpen(!isSidebarOpen)}
        >
          <Icon
            icon={isSidebarOpen ? "mi:close" : "mi:menu"}
            className={isSidebarOpen ? "w-4 h-4" : "w-5 h-5"}
          />
        </button>
      </div>

      <div className="w-80 grow overflow-hidden">
        <div className="h-1/2 overflow-y-auto flex flex-col">
          <SidebarOptions
            forceClose={!isSidebarOpen}
            onSelectOption={() => setIsSidebarOpen(true)}
          />
        </div>

        <AnimatePresence>
          {isSidebarOpen && (
            <motion.div
              className="h-1/2 overflow-y-auto flex flex-col"
              initial={{ opacity: 0, filter: "blur(8px)" }}
              animate={{ opacity: 1, filter: "blur(0px)" }}
              exit={{ opacity: 0, filter: "blur(8px)" }}
              transition={{ duration: 0.15 }}
            >
              <div className="grow p-4 leading-tight bg-stone-200 text-stone-700 dark:bg-stone-700 dark:text-stone-300 text-sm">
                <p>
                  JavaFiddle is an online tool to <b>build</b> and <b>share</b>{" "}
                  snippets of Java code.
                </p>

                <hr className="my-6 border-stone-300 dark:border-stone-600" />

                <ul className="list-disc space-y-2 ml-3">
                  <li>
                    Runs entirely <b>in your browser</b>.
                  </li>
                  <li>Supports all of Java SE 8, including Swing.</li>
                </ul>

                <div className="my-6 flex items-center justify-center gap-2">
                  <a
                    className="rounded bg-[#5865F2] text-white font-semibold px-3 py-2 inline-flex items-center justify-center gap-2"
                    href="https://discord.gg/qBMHpK9Kqve"
                  >
                    <Icon icon="fa-brands:discord" className="w-4 h-4" />
                    Discord server
                  </a>
                  <a
                    className="rounded hover:bg-stone-800 text-stone-950 dark:text-white hover:text-white font-semibold px-3 py-2 inline-flex items-center justify-center gap-2"
                    href="https://github.com/leaningtech/javafiddle"
                  >
                    <Icon icon="fa-brands:github" className="w-4 h-4" />
                    View source
                  </a>
                </div>
              </div>

              <a
                className="p-6 border-t-4 border-orange-500 bg-stone-300 dark:bg-stone-600 text-stone-800 dark:text-stone-100"
                href="https://labs.leaningtech.com/cheerpj"
              >
                <div className="text-2xl font-bold mb-4">CheerpJ</div>
                <p className="text-sm leading-tight">
                  Run Java applications in the browser with CheerpJ. Supports
                  applets, JNLP, Web Start, Oracle Forms, and more.
                </p>
              </a>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </aside>
  );
}
