# JavaFiddle

JavaFiddle is an online, browser-based Java IDE built with Next.js. Create and share Java code snippets and Swing applications for free!

## Features

- **Online Java IDE**: Write, compile, and run Java code directly in your browser
- **CheerpJ Integration**: Powered by CheerpJ for seamless Java execution
- **Code Sharing**: Share your Java fiddles via URL with automatic compression
- **Theme Support**: Light and dark themes with system preference detection
- **File Management**: Multi-file support with tabs and file operations
- **Favorites System**: Save and organize your favorite code snippets
- **Real-time Compilation**: Automatic compilation with error highlighting
- **Responsive Design**: Works on desktop and mobile devices

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see JavaFiddle.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
