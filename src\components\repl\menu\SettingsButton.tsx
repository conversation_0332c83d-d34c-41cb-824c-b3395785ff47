"use client";

import { useState, useRef, useEffect } from "react";
import { Settings } from "lucide-react";
import SettingsPanel from "./SettingsPanel";

export default function SettingsButton() {
  const [isOpen, setIsOpen] = useState(false);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const panelRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        panelRef.current &&
        !panelRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isOpen]);

  return (
    <li className="relative">
      <button
        ref={buttonRef}
        onClick={() => setIsOpen(!isOpen)}
        className="text-sm flex items-center rounded bg-stone-100 hover:bg-stone-200 dark:bg-stone-800 dark:hover:bg-stone-700 font-semibold px-2 py-1 h-8"
        title="Settings"
      >
        <Settings className="w-4 h-4" />
      </button>
      
      {isOpen && (
        <div
          ref={panelRef}
          className="absolute right-0 top-full mt-2 z-50"
        >
          <SettingsPanel onClose={() => setIsOpen(false)} />
        </div>
      )}
    </li>
  );
}
