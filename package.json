{"name": "javafiddle", "version": "1.0.0", "description": "JavaFiddle - Online Java IDE built with Next.js", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@codemirror/commands": "^6.8.1", "@codemirror/lang-java": "^6.0.2", "@codemirror/language": "^6.11.2", "@codemirror/lint": "^6.8.5", "@codemirror/state": "^6.5.2", "@codemirror/view": "^6.38.1", "@iconify/react": "^6.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "codemirror": "^6.0.2", "date-fns": "^4.1.0", "framer-motion": "^12.23.12", "lucide-react": "^0.536.0", "lz-string": "^1.5.0", "next": "15.4.5", "react": "19.1.0", "react-dom": "19.1.0", "react-split": "^2.0.14", "tailwind-merge": "^3.3.1", "thememirror": "^2.0.1", "zod": "^4.0.14", "zustand": "^5.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.5", "tailwindcss": "^4", "tw-animate-css": "^1.3.6", "typescript": "^5"}}