.layout
{
  display: flex;

}

.vertical
{
  flex-direction: column;

}

.flex
{
  flex-grow: 1;

}

.horizontal
{
  flex-direction: row;

}

.covering
{
  width: 100%; height: 100%; margin: 0em;
              left: 0em; top: 0em;
              position: fixed;

}

.red
{
  background-color: red;
}

.yellow
{
  background-color: yellow;
}

.blue
{
  background-color: blue;

}

.green
{
  background-color: green;

}

.black
{
  background-color: black;

}

.fill-w
{
  width: 100%;
}

.fill-h
{
  height: 100%;

}

.center-x
{
  margin-left: auto;
  margin-right: auto;
}

.center-y
{

  margin-top: auto;
  margin-bottom: auto;


}

