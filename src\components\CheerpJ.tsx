import { useEffect, useRef } from "react";
import {
  useFilesStore,
  useExecutionStore,
  useSettingsStore,
  type File,
} from "./repl/state";
import { debounceFunction } from "@/lib/utilities";

export default function CheerpjRunner() {
  const cjConsoleRef = useRef<HTMLDivElement>(null);
  const cjOutputRef = useRef<HTMLDivElement>(null);
  const cjOutputObserver = useRef<MutationObserver | null>(null);

  const { files } = useFilesStore();
  const {
    isRunning,
    isSaved,
    runCode,
    compileLog,
    setIsRunning,
    setIsSaved,
    setRunCode,
    setCompileLog,
  } = useExecutionStore();
  const { autoRun } = useSettingsStore();

  const startCheerpj = async () => {
    await cheerpjInit({ status: "none" });
    const outputElement = document.getElementById("output");
    if (outputElement) {
      cheerpjCreateDisplay(-1, -1, outputElement);
    }
  };

  const deriveMainClass = (file: File) => {
    const className =
      file.path.split("/").pop()?.replace(".java", "") || "Main";
    const match = file.content.match(/package\s+(.+);/);
    if (match && match.length > 1) {
      const packageName = match[1];
      return `${packageName}.${className}`;
    }
    return className;
  };

  const runCheerpj = async () => {
    if (isRunning) return;

    console.info("compileAndRun");
    setIsRunning(true);
    if (cjConsoleRef.current) cjConsoleRef.current.innerHTML = "";
    if (cjOutputRef.current) cjOutputRef.current.innerHTML = "";

    const classPath = "/app/tools.jar:/files/";
    const sourceFiles = files.map((file) => "/str/" + file.path);
    const code = await cheerpjRunMain(
      "com.sun.tools.javac.Main",
      classPath,
      ...sourceFiles,
      "-d",
      "/files/",
      "-Xlint"
    );

    if (code === 0) {
      await cheerpjRunMain(deriveMainClass(files[0]), classPath);
    }

    if (isRunning) setIsRunning(false);
    setCompileLog(cjConsoleRef.current?.innerText || "");
  };

  const debounceRunCheerpj = debounceFunction(runCheerpj, 500);

  useEffect(() => {
    const setup = async () => {
      await startCheerpj();

      const cjConsoleElem = document.getElementById("console");
      const cjOutputElem = document.getElementById("cheerpjDisplay");
      cjOutputElem?.classList.remove("cheerpjLoading");

      // Set up mutation observer
      if (cjConsoleElem && cjOutputElem) {
        cjOutputObserver.current = new MutationObserver(() => {
          if (
            isRunning &&
            (cjConsoleElem.innerHTML || cjOutputElem.innerHTML)
          ) {
            setIsRunning(false);
            if (!isSaved) {
              // Trigger a re-render to update files
              useFilesStore.setState((state) => ({ ...state }));
            }
          }
        });

        cjOutputObserver.current.observe(cjConsoleElem, {
          childList: true,
          subtree: true,
        });
        cjOutputObserver.current.observe(cjOutputElem, {
          childList: true,
          subtree: true,
        });
      }

      await runCheerpj();
    };

    setup();

    return () => {
      cjOutputObserver.current?.disconnect();
    };
  }, []);

  // Handle files changes
  useEffect(() => {
    if (isRunning) {
      setIsSaved(false);
    } else {
      try {
        for (const file of files) {
          cheerpjAddStringFile("/str/" + file.path, file.content);
        }
        setIsSaved(true);
        if (autoRun) setRunCode(true);
      } catch (err) {
        console.error("Error writing files to CheerpJ", err);
      }
    }
  }, [files, isRunning, autoRun, setIsSaved, setRunCode]);

  // Handle run code changes
  useEffect(() => {
    if (runCode) {
      setRunCode(false);
      autoRun ? debounceRunCheerpj() : runCheerpj();
    }
  }, [runCode, autoRun, setRunCode, debounceRunCheerpj]);

  return (
    <>
      <div id="console" ref={cjConsoleRef}></div>
      <div id="output" ref={cjOutputRef}></div>
    </>
  );
}
