import { useEffect, useRef, useCallback } from "react";
import {
  useFilesStore,
  useExecutionStore,
  useSettingsStore,
  type File,
} from "./repl/state";
import { debounceFunction } from "@/lib/utilities";

export default function CheerpjRunner() {
  const cjConsoleRef = useRef<HTMLDivElement>(null);
  const cjOutputRef = useRef<HTMLDivElement>(null);
  const cjOutputObserver = useRef<MutationObserver | null>(null);
  const cheerpjInitialized = useRef<boolean>(false);
  const compilationTimeout = useRef<NodeJS.Timeout | null>(null);

  const { files } = useFilesStore();
  const {
    isRunning,
    isSaved,
    runCode,
    compileLog,
    hasError,
    errorMessage,
    setIsRunning,
    setIsSaved,
    setRunCode,
    setCompileLog,
    setError,
    clearError,
  } = useExecutionStore();
  const { autoRun } = useSettingsStore();

  const startCheerpj = useCallback(async () => {
    if (cheerpjInitialized.current) {
      console.log("CheerpJ already initialized");
      return;
    }

    try {
      console.log("Checking if CheerpJ is available...");

      // Wait for CheerpJ to be available
      let attempts = 0;
      const maxAttempts = 50; // 5 seconds max

      while (attempts < maxAttempts) {
        if (
          typeof window !== "undefined" &&
          typeof window.cheerpjInit === "function" &&
          typeof window.cheerpjAddStringFile === "function" &&
          typeof window.cheerpjRunMain === "function" &&
          typeof window.cheerpjCreateDisplay === "function"
        ) {
          console.log("CheerpJ functions are available");
          break;
        }

        console.log(
          `Waiting for CheerpJ... attempt ${attempts + 1}/${maxAttempts}`
        );
        await new Promise((resolve) => setTimeout(resolve, 100));
        attempts++;
      }

      if (attempts >= maxAttempts) {
        throw new Error("CheerpJ failed to load after 5 seconds");
      }

      console.log("Initializing CheerpJ...");
      await window.cheerpjInit({ status: "none" });
      cheerpjInitialized.current = true;
      console.log("CheerpJ initialized successfully");

      const outputElement = document.getElementById("output");
      if (outputElement) {
        console.log("Creating CheerpJ display...");
        window.cheerpjCreateDisplay(-1, -1, outputElement);
        console.log("CheerpJ display created");
      } else {
        console.error("Output element not found for CheerpJ display");
      }
    } catch (error) {
      console.error("Failed to initialize CheerpJ:", error);
      cheerpjInitialized.current = false;
      const errorMsg = `CheerpJ initialization failed: ${
        error instanceof Error ? error.message : String(error)
      }`;
      setError(true, errorMsg);
      throw error;
    }
  }, [setError]);

  const deriveMainClass = (file: File) => {
    const className =
      file.path.split("/").pop()?.replace(".java", "") || "Main";
    const match = file.content.match(/package\s+(.+);/);
    if (match && match.length > 1) {
      const packageName = match[1];
      return `${packageName}.${className}`;
    }
    return className;
  };

  const runCheerpj = useCallback(async () => {
    if (isRunning) {
      console.log("Compilation already running, skipping...");
      return;
    }

    if (!cheerpjInitialized.current) {
      console.error("CheerpJ not initialized, cannot run compilation");
      return;
    }

    console.log("Starting Java compilation and execution...");
    setIsRunning(true);
    clearError(); // Clear any previous errors

    // Clear previous output
    if (cjConsoleRef.current) cjConsoleRef.current.innerHTML = "";
    if (cjOutputRef.current) cjOutputRef.current.innerHTML = "";

    // Set a timeout to prevent infinite loading (30 seconds max)
    compilationTimeout.current = setTimeout(() => {
      console.error("Compilation timeout - forcing completion");
      setIsRunning(false);
      setCompileLog("Compilation timed out after 30 seconds");
    }, 30000);

    try {
      const classPath = "/app/tools.jar:/files/";
      const sourceFiles = files.map((file) => "/str/" + file.path);

      console.log("Compiling Java files:", sourceFiles);
      const compilationCode = await window.cheerpjRunMain(
        "com.sun.tools.javac.Main",
        classPath,
        ...sourceFiles,
        "-d",
        "/files/",
        "-Xlint"
      );

      console.log("Compilation result code:", compilationCode);

      if (compilationCode === 0) {
        console.log("Compilation successful, running main class...");
        const mainClass = deriveMainClass(files[0]);
        console.log("Running main class:", mainClass);
        await window.cheerpjRunMain(mainClass, classPath);
        console.log("Execution completed");
      } else {
        console.log("Compilation failed with code:", compilationCode);
      }

      // Update compile log
      const logContent = cjConsoleRef.current?.innerText || "";
      setCompileLog(logContent);
      console.log("Compile log updated:", logContent);
    } catch (error) {
      console.error("Error during compilation/execution:", error);
      const errorMsg = `Compilation error: ${
        error instanceof Error ? error.message : String(error)
      }`;
      setCompileLog(errorMsg);
      setError(true, errorMsg);
    } finally {
      // Clear timeout and ensure we exit running state
      if (compilationTimeout.current) {
        clearTimeout(compilationTimeout.current);
        compilationTimeout.current = null;
      }

      // Force exit running state after a brief delay to allow DOM updates
      setTimeout(() => {
        console.log("Forcing exit from running state");
        setIsRunning(false);
      }, 1000);
    }
  }, [isRunning, files, setIsRunning, setCompileLog, setError, clearError]);

  const debounceRunCheerpj = debounceFunction(runCheerpj, 500);

  useEffect(() => {
    const setup = async () => {
      try {
        console.log("Setting up CheerpJ environment...");
        await startCheerpj();

        // Wait a bit for CheerpJ to fully initialize
        await new Promise((resolve) => setTimeout(resolve, 1000));

        const cjConsoleElem = document.getElementById("console");
        const cjOutputElem = document.getElementById("output");

        console.log("Console element found:", !!cjConsoleElem);
        console.log("Output element found:", !!cjOutputElem);

        // Look for the actual CheerpJ display element that gets created
        const findCheerpjDisplay = () => {
          return (
            cjOutputElem?.querySelector("canvas") ||
            cjOutputElem?.querySelector('[id*="cheerpj"]') ||
            document.getElementById("cheerpjDisplay")
          );
        };

        // Set up mutation observer with better element detection
        if (cjConsoleElem || cjOutputElem) {
          cjOutputObserver.current = new MutationObserver(() => {
            console.log("DOM mutation detected, checking for completion...");

            if (!isRunning) {
              console.log("Not running, ignoring mutation");
              return;
            }

            const hasConsoleContent =
              cjConsoleElem && cjConsoleElem.innerHTML.trim();
            const hasOutputContent =
              cjOutputElem && cjOutputElem.innerHTML.trim();
            const cheerpjDisplay = findCheerpjDisplay();
            const hasDisplayContent =
              cheerpjDisplay && cheerpjDisplay.innerHTML;

            console.log("Content check:", {
              hasConsoleContent: !!hasConsoleContent,
              hasOutputContent: !!hasOutputContent,
              hasDisplayContent: !!hasDisplayContent,
              cheerpjDisplayFound: !!cheerpjDisplay,
            });

            if (hasConsoleContent || hasOutputContent || hasDisplayContent) {
              console.log("Content detected, stopping execution");
              setIsRunning(false);
              if (!isSaved) {
                // Trigger a re-render to update files
                useFilesStore.setState((state) => ({ ...state }));
              }
            }
          });

          // Observe console element
          if (cjConsoleElem) {
            cjOutputObserver.current.observe(cjConsoleElem, {
              childList: true,
              subtree: true,
              characterData: true,
            });
            console.log("Observing console element");
          }

          // Observe output element
          if (cjOutputElem) {
            cjOutputObserver.current.observe(cjOutputElem, {
              childList: true,
              subtree: true,
              characterData: true,
            });
            console.log("Observing output element");
          }
        }

        console.log("CheerpJ setup completed, running initial compilation...");
        await runCheerpj();
      } catch (error) {
        console.error("Failed to setup CheerpJ:", error);
        setIsRunning(false);
      }
    };

    setup();

    return () => {
      console.log("Cleaning up CheerpJ component...");
      cjOutputObserver.current?.disconnect();
      if (compilationTimeout.current) {
        clearTimeout(compilationTimeout.current);
      }
      // Reset initialization flag to allow re-initialization if component remounts
      cheerpjInitialized.current = false;
    };
  }, [startCheerpj, runCheerpj, isRunning, isSaved, setIsRunning]);

  // Handle files changes
  useEffect(() => {
    if (isRunning) {
      console.log("Files changed while running, marking as unsaved");
      setIsSaved(false);
    } else {
      try {
        if (!cheerpjInitialized.current) {
          console.log("CheerpJ not initialized yet, skipping file update");
          return;
        }

        console.log("Updating files in CheerpJ filesystem...");
        for (const file of files) {
          window.cheerpjAddStringFile("/str/" + file.path, file.content);
        }
        setIsSaved(true);
        console.log("Files updated successfully");

        if (autoRun) {
          console.log("Auto-run enabled, triggering compilation");
          setRunCode(true);
        }
      } catch (err) {
        console.error("Error writing files to CheerpJ:", err);
        const errorMsg = `Error updating files: ${
          err instanceof Error ? err.message : String(err)
        }`;
        setCompileLog(errorMsg);
        setError(true, errorMsg);
      }
    }
  }, [
    files,
    isRunning,
    autoRun,
    setIsSaved,
    setRunCode,
    setCompileLog,
    setError,
  ]);

  // Handle run code changes
  useEffect(() => {
    if (runCode) {
      console.log("Run code triggered");
      setRunCode(false);
      if (autoRun) {
        console.log("Using debounced run for auto-run");
        debounceRunCheerpj();
      } else {
        console.log("Running immediately");
        runCheerpj();
      }
    }
  }, [runCode, autoRun, setRunCode, debounceRunCheerpj, runCheerpj]);

  return (
    <>
      <div id="console" ref={cjConsoleRef}></div>
      <div id="output" ref={cjOutputRef}></div>
    </>
  );
}
