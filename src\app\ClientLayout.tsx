"use client";

import { ReactNode, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  decompress,
  defaultFiddle,
  defaultFiddleComp,
} from "@/lib/compress-fiddle";
import { useFilesStore, useFiddleMetaStore, useSettingsStore } from "@/components/repl/state";

interface ClientLayoutProps {
  children: ReactNode;
}

export default function ClientLayout({ children }: ClientLayoutProps) {
  const router = useRouter();
  const { setFiles } = useFilesStore();
  const { setFiddleTitle, setFiddleUpdated } = useFiddleMetaStore();
  const { theme } = useSettingsStore();

  const setFiddle = () => {
    if (typeof window === 'undefined') return;
    
    const fragmentURL = window.location.hash.slice(1);
    let fiddle;
    if (!fragmentURL) {
      fiddle = defaultFiddle;
      router.push(`/#${defaultFiddleComp}`);
    } else {
      try {
        fiddle = decompress(fragmentURL);
      } catch (error) {
        console.error('Failed to decompress fiddle:', error);
        fiddle = defaultFiddle;
        router.push(`/#${defaultFiddleComp}`);
      }
    }
    setFiles(fiddle.files);
    setFiddleTitle(fiddle.title);
    setFiddleUpdated(fiddle.updated);
  };

  useEffect(() => {
    setFiddle();
    window.addEventListener("popstate", setFiddle);
    return () => window.removeEventListener("popstate", setFiddle);
  }, []);

  // Apply theme to document element
  useEffect(() => {
    if (typeof window === 'undefined') return;
    
    const applyTheme = () => {
      const root = document.documentElement;
      if (theme === 'dark') {
        root.classList.add('dark');
      } else if (theme === 'light') {
        root.classList.remove('dark');
      } else {
        // System theme
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        if (prefersDark) {
          root.classList.add('dark');
        } else {
          root.classList.remove('dark');
        }
      }
    };

    applyTheme();

    if (theme === 'system') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      mediaQuery.addEventListener('change', applyTheme);
      return () => mediaQuery.removeEventListener('change', applyTheme);
    }
  }, [theme]);

  return (
    <div className="bg-white text-black dark:bg-stone-900 dark:text-white min-h-screen">
      {children}
    </div>
  );
}
