"use client";

import { useSettingsStore } from "../state";
import ThemeSwitcher from "./ThemeSwitcher";

interface SettingsPanelProps {
  onClose: () => void;
}

export default function SettingsPanel({
  onClose: _onClose,
}: SettingsPanelProps) {
  const { autoRun, setAutoRun } = useSettingsStore();

  return (
    <div className="bg-white dark:bg-stone-800 border border-stone-200 dark:border-stone-700 rounded-lg shadow-lg p-4 min-w-64">
      <h3 className="text-lg font-semibold text-stone-900 dark:text-stone-100 mb-4">
        Settings
      </h3>

      <div className="space-y-4">
        {/* Auto-run setting */}
        <div className="flex items-center justify-between">
          <label
            htmlFor="auto-run"
            className="text-sm text-stone-700 dark:text-stone-300"
          >
            Auto-run code
          </label>
          <input
            id="auto-run"
            type="checkbox"
            checked={autoRun}
            onChange={(e) => setAutoRun(e.target.checked)}
            className="rounded border-stone-300 dark:border-stone-600 text-orange-500 focus:ring-orange-500"
          />
        </div>

        {/* Theme switcher */}
        <div className="flex items-center justify-between">
          <label className="text-sm text-stone-700 dark:text-stone-300">
            Theme
          </label>
          <ThemeSwitcher />
        </div>
      </div>
    </div>
  );
}
