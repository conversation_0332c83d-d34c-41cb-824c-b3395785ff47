declare global {
  interface Window {
    cheerpjInit?: (options?: object) => Promise<void>;
    cheerpjRunMain: (className: string, ...args: string[]) => Promise<unknown>;
    cheerpjRunJar: (jarPath: string, ...args: string[]) => Promise<unknown>;
    cheerpjRunLibrary: (libraryName: string) => Promise<unknown>;
    cheerpjCreateDisplay: (
      width: number,
      height: number,
      element?: HTMLElement
    ) => HTMLElement;
    cjFileBlob: (path: string, blob: Blob) => void;
    cjGetRuntimeResources: () => unknown;
    cjGetProguardConfiguration: () => string;
    dumpMethod: (className: string, methodName: string) => void;
    dumpClass: (className: string) => void;
    dumpAllThreads: () => void;
  }
}

export {};
