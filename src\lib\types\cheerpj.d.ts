declare const cheerpjInit: (options?: object) => Promise<void>;
declare const cheerpjRunMain: (
  className: string,
  ...args: string[]
) => Promise<unknown>;
declare const cheerpjRunJar: (
  jarPath: string,
  ...args: string[]
) => Promise<unknown>;
declare const cheerpjRunLibrary: (libraryName: string) => Promise<unknown>;
declare const cheerpjCreateDisplay: (
  width: number,
  height: number,
  element?: HTMLElement
) => HTMLElement;
declare const cjFileBlob: (path: string, blob: Blob) => void;
declare const cjGetRuntimeResources: () => unknown;
declare const cjGetProguardConfiguration: () => string;
declare const dumpMethod: (className: string, methodName: string) => void;
declare const dumpClass: (className: string) => void;
declare const dumpAllThreads: () => void;
declare const cheerpjAddStringFile: (path: string, content: string) => void;
